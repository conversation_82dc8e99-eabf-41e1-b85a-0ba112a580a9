// Teste das correções implementadas no PIX
// Simulação das funções para teste rápido

// Função para formatar campo EMV
function formatField(id, value) {
  const length = value.length.toString().padStart(2, '0');
  return `${id}${length}${value}`;
}

// Função para formatar subcampos
function formatSubField(id, value) {
  const length = value.length.toString().padStart(2, '0');
  return `${id}${length}${value}`;
}

// Implementação CRC16-CCITT
function calculateCRC16(payload) {
  let crc = 0xffff;

  for (let i = 0; i < payload.length; i++) {
    const byte = payload.charCodeAt(i);
    crc ^= byte << 8;

    for (let bit = 0; bit < 8; bit++) {
      if (crc & 0x8000) {
        crc = ((crc << 1) ^ 0x1021) & 0xffff;
      } else {
        crc = (crc << 1) & 0xffff;
      }
    }
  }

  return crc.toString(16).toUpperCase().padStart(4, '0');
}

// Função para gerar payload PIX corrigida
function generatePixPayload(pixInfo) {
  const payloadFormatIndicator = formatField('00', '01');

  const gui = formatSubField('00', 'br.gov.bcb.pix');
  const pixKey = formatSubField('01', pixInfo.key);
  const merchantAccountInfo = gui + pixKey;
  const merchantAccountInformation = formatField('26', merchantAccountInfo);

  const merchantCategoryCode = formatField('52', '0000');
  const transactionCurrency = formatField('53', '986');

  // CORREÇÃO: Formatar valor corretamente
  let transactionAmount;
  if (pixInfo.amount % 1 === 0) {
    transactionAmount = pixInfo.amount.toString(); // Sem casas decimais
  } else {
    transactionAmount = pixInfo.amount.toFixed(2); // Com casas decimais
  }
  const transactionAmountField = formatField('54', transactionAmount);

  const countryCode = formatField('58', 'BR');
  const merchantName = pixInfo.name.substring(0, 25).toUpperCase();
  const merchantNameField = formatField('59', merchantName);
  const merchantCity = pixInfo.city.substring(0, 15).toUpperCase();
  const merchantCityField = formatField('60', merchantCity);

  const payloadWithoutCrc =
    payloadFormatIndicator +
    merchantAccountInformation +
    merchantCategoryCode +
    transactionCurrency +
    transactionAmountField +
    countryCode +
    merchantNameField +
    merchantCityField;

  const payloadForCrc = payloadWithoutCrc + '6304';
  const crc = calculateCRC16(payloadForCrc);

  return payloadWithoutCrc + '6304' + crc;
}

// Função para decodificar payload PIX (simplificada)
function decodePixPayload(payload) {
  const result = {};
  let index = 0;

  while (index < payload.length - 4) {
    // -4 para o CRC no final
    const id = payload.substring(index, index + 2);
    const length = parseInt(payload.substring(index + 2, index + 4), 10);
    const value = payload.substring(index + 4, index + 4 + length);

    index += 4 + length;

    switch (id) {
      case '00':
        result.payloadFormatIndicator = value;
        break;
      case '26':
        // Decodificar subcampos do Merchant Account Information
        result.merchantAccountInfo = {};
        let subIndex = 0;
        while (subIndex < value.length) {
          const subId = value.substring(subIndex, subIndex + 2);
          const subLength = parseInt(
            value.substring(subIndex + 2, subIndex + 4),
            10
          );
          const subValue = value.substring(
            subIndex + 4,
            subIndex + 4 + subLength
          );
          subIndex += 4 + subLength;

          if (subId === '00') result.merchantAccountInfo.gui = subValue;
          if (subId === '01') result.merchantAccountInfo.key = subValue;
        }
        break;
      case '52':
        result.merchantCategoryCode = value;
        break;
      case '53':
        result.transactionCurrency = value;
        break;
      case '54':
        result.transactionAmount = value;
        break;
      case '58':
        result.countryCode = value;
        break;
      case '59':
        result.merchantName = value;
        break;
      case '60':
        result.merchantCity = value;
        break;
    }
  }

  // Extrair CRC16 dos últimos 4 caracteres
  result.crc16 = payload.substring(payload.length - 4);

  return result;
}

console.log('=== TESTE DAS CORREÇÕES PIX ===');

// Teste 1: Valor inteiro (problema principal identificado)
console.log('\n--- TESTE 1: Valor Inteiro ---');
const testData1 = {
  key: '***********',
  name: 'Wellington Oliveira',
  city: 'Franca',
  amount: 2590,
};

const payload1 = generatePixPayload(testData1);
console.log('Payload gerado:', payload1);

const decoded1 = decodePixPayload(payload1);
console.log('Valor formatado:', decoded1.transactionAmount);
console.log('Esperado: "2590" (sem .00)');

const validation1 = validatePixPayload(payload1);
console.log('Validação:', validation1.isValid ? '✅ Válido' : '❌ Inválido');
if (!validation1.isValid) {
  console.log('Erros:', validation1.errors);
}

// Teste 2: Valor com decimais
console.log('\n--- TESTE 2: Valor com Decimais ---');
const testData2 = {
  key: '***********',
  name: 'Wellington Oliveira',
  city: 'Franca',
  amount: 25.9,
};

const payload2 = generatePixPayload(testData2);
console.log('Payload gerado:', payload2);

const decoded2 = decodePixPayload(payload2);
console.log('Valor formatado:', decoded2.transactionAmount);
console.log('Esperado: "25.90"');

const validation2 = validatePixPayload(payload2);
console.log('Validação:', validation2.isValid ? '✅ Válido' : '❌ Inválido');
if (!validation2.isValid) {
  console.log('Erros:', validation2.errors);
}

// Teste 3: Comparação com payload original (do log)
console.log('\n--- TESTE 3: Comparação com Payload Original ---');
const originalPayload =
  '00020126330014br.gov.bcb.pix0111***********52040000530398654072590.005802BR5919WELLINGTON OLIVEIRA6006FRANCA6304C764';
console.log('Payload original (com problema):', originalPayload);

const decodedOriginal = decodePixPayload(originalPayload);
console.log('Valor original:', decodedOriginal.transactionAmount); // Deve ser "2590.00"

const newPayload = generatePixPayload(testData1);
console.log('Payload corrigido:', newPayload);

const decodedNew = decodePixPayload(newPayload);
console.log('Valor corrigido:', decodedNew.transactionAmount); // Deve ser "2590"

console.log('\n=== ANÁLISE DAS DIFERENÇAS ===');
console.log('Diferença principal: Formato do valor monetário');
console.log('Original: 2590.00 (com casas decimais desnecessárias)');
console.log('Corrigido: 2590 (sem casas decimais para valores inteiros)');
console.log('');
console.log('Esta correção deve resolver o problema de rejeição pelos bancos!');
console.log('===============================');
